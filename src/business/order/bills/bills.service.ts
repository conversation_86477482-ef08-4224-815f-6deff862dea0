import {
  Injectable,
  InternalServerErrorException,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
} from '@nestjs/common';
import { PuppeteerService } from '../../../utils/puppeteer.service';
import { OrderBills } from './bills.controller';
import {
  generateBillOfLadingHTML,
  BillOfLadingData,
} from './templates/billOfLading';
import {
  generateShippingLabelHTML,
  ShippingLabelData,
} from './templates/shippingLabel';
import { generateWayBillHTML, WayBillData } from './templates/wayBill';

@Injectable()
export class BillsService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(BillsService.name);

  constructor(private readonly puppeteerService: PuppeteerService) {}

  async onModuleInit() {
    // Initialize browser in PuppeteerService
    await this.puppeteerService.initBrowser();
    this.logger.log('BillsService initialized with TypeScript templates');
  }

  async onModuleDestroy() {
    // PuppeteerService handles browser cleanup
  }

  async getBill(
    billName: OrderBills = OrderBills.BILL_OF_LADING,
    data: any = {},
  ): Promise<Buffer> {
    try {
      let html: string;

      // Generate HTML based on the bill type
      switch (billName) {
        case OrderBills.BILL_OF_LADING:
          html = generateBillOfLadingHTML(data as BillOfLadingData);
          break;
        case OrderBills.WAY_BILL:
          html = generateWayBillHTML(data as WayBillData);
          break;
        case OrderBills.SHIPPING_LABEL:
          html = generateShippingLabelHTML(data as ShippingLabelData);
          break;
        default:
          html = generateBillOfLadingHTML(data as BillOfLadingData);
      }

      // Generate PDF using PuppeteerService
      const pdfBuffer = await this.puppeteerService.generatePDFFromHTML(html);
      return pdfBuffer;
    } catch (error: any) {
      this.logger.error('Failed to generate bill PDF', error?.stack || error);
      throw new InternalServerErrorException('Failed to generate bill PDF');
    }
  }

  // Convenience methods for each template type
  async getBillOfLading(data: BillOfLadingData = {}): Promise<Buffer> {
    return this.getBill(OrderBills.BILL_OF_LADING, data);
  }

  async getWayBill(data: WayBillData = {}): Promise<Buffer> {
    return this.getBill(OrderBills.WAY_BILL, data);
  }

  async getShippingLabel(data: ShippingLabelData = {}): Promise<Buffer> {
    return this.getBill(OrderBills.SHIPPING_LABEL, data);
  }
}
