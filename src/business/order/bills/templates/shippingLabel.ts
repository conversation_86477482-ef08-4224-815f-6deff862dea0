export interface ShippingLabelData {
  trackingNumber?: string;
  senderName?: string;
  senderAddress?: string;
  receiverName?: string;
  receiverAddress?: string;
  serviceLevel?: string;
  weight?: string;
  dimensions?: string;
  deliveryDate?: string;
  specialInstructions?: string;
}

export const generateShippingLabelHTML = (
  data: ShippingLabelData = {},
): string => {
  return `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Shipping Label</title>
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background-color: white;
        color: #000;
        font-size: 12px;
        line-height: 1.4;
        width: 4in;
        height: 6in;
        padding: 0.25in;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      .header {
        text-align: center;
        margin-bottom: 16px;
        border-bottom: 2px solid #000;
        padding-bottom: 8px;
      }
      .company-name {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 4px;
      }
      .tracking-section {
        text-align: center;
        margin-bottom: 16px;
        padding: 8px;
        border: 2px solid #000;
        background-color: #f0f0f0;
      }
      .tracking-number {
        font-size: 18px;
        font-weight: bold;
        letter-spacing: 2px;
      }
      .addresses {
        margin-bottom: 16px;
      }
      .address-section {
        margin-bottom: 12px;
        padding: 8px;
        border: 1px solid #000;
      }
      .address-label {
        font-weight: bold;
        font-size: 10px;
        text-transform: uppercase;
        margin-bottom: 4px;
      }
      .address-content {
        font-size: 12px;
        line-height: 1.3;
      }
      .service-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        font-size: 10px;
      }
      .service-info div {
        text-align: center;
        flex: 1;
        padding: 4px;
        border: 1px solid #000;
        margin-right: 2px;
      }
      .service-info div:last-child {
        margin-right: 0;
      }
      .barcode-section {
        text-align: center;
        margin-bottom: 16px;
        padding: 8px;
        border: 1px solid #000;
      }
      .barcode-placeholder {
        height: 40px;
        background-color: #000;
        background-image: repeating-linear-gradient(90deg, #000 0px, #000 2px, #fff 2px, #fff 4px);
        margin-bottom: 4px;
      }
      .special-instructions {
        font-size: 10px;
        border: 1px solid #000;
        padding: 8px;
        min-height: 40px;
      }
      .footer {
        position: absolute;
        bottom: 0.25in;
        left: 0.25in;
        right: 0.25in;
        text-align: center;
        font-size: 8px;
        border-top: 1px solid #000;
        padding-top: 4px;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="company-name">LUMIGO TRANSPORT</div>
      <div>Phone: ************ | www.lumigotransport.ca</div>
    </div>

    <div class="tracking-section">
      <div style="font-size: 12px; margin-bottom: 4px;">TRACKING NUMBER</div>
      <div class="tracking-number">${data.trackingNumber || 'TRK-2025-001234'}</div>
    </div>

    <div class="addresses">
      <div class="address-section">
        <div class="address-label">FROM:</div>
        <div class="address-content">
          ${data.senderName || 'Lumigo Transport Solutions'}<br>
          ${data.senderAddress || '123 Main Street<br>Montreal, QC H1A 1A1'}
        </div>
      </div>

      <div class="address-section">
        <div class="address-label">TO:</div>
        <div class="address-content">
          ${data.receiverName || 'ABC Company Ltd.'}<br>
          ${data.receiverAddress || '456 Business Ave<br>Toronto, ON M5V 3A8'}
        </div>
      </div>
    </div>

    <div class="service-info">
      <div>
        <strong>SERVICE</strong><br>
        ${data.serviceLevel || 'EXPRESS'}
      </div>
      <div>
        <strong>WEIGHT</strong><br>
        ${data.weight || '15 lbs'}
      </div>
      <div>
        <strong>DELIVERY</strong><br>
        ${data.deliveryDate || 'Same Day'}
      </div>
    </div>

    <div class="barcode-section">
      <div class="barcode-placeholder"></div>
      <div style="font-size: 10px;">${data.trackingNumber || 'TRK-2025-001234'}</div>
    </div>

    <div class="special-instructions">
      <strong>SPECIAL INSTRUCTIONS:</strong><br>
      ${data.specialInstructions || 'Handle with care. Fragile items.'}
    </div>

    <div class="footer">
      Lumigo Transport Solutions - Professional Delivery Services
    </div>
  </body>
</html>`;
};
