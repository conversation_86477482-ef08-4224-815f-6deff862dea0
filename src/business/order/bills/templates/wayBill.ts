export interface WayBillData {
  wayBillNumber?: string;
  date?: string;
  driverName?: string;
  vehicleNumber?: string;
  routeNumber?: string;
  senderName?: string;
  senderAddress?: string;
  receiverName?: string;
  receiverAddress?: string;
  packageCount?: string;
  totalWeight?: string;
  serviceType?: string;
  specialInstructions?: string;
  pickupTime?: string;
  deliveryTime?: string;
  cod?: string;
  declaredValue?: string;
}

export const generateWayBillHTML = (data: WayBillData = {}): string => {
  return `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Way Bill</title>
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background-color: white;
        color: #1a202c;
        font-size: 14px;
        line-height: 1.5;
        max-width: 8.5in;
        margin: 0 auto;
        padding: 0.5in;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 2px solid #1a202c;
      }
      .company-info {
        flex: 1;
      }
      .company-name {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 8px;
      }
      .company-details {
        font-size: 12px;
        color: #666;
      }
      .document-info {
        text-align: right;
      }
      .document-title {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 8px;
      }
      .document-number {
        font-size: 16px;
        font-weight: bold;
        background-color: #f0f0f0;
        padding: 8px 16px;
        border: 2px solid #1a202c;
      }
      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
        margin-bottom: 24px;
      }
      .info-section {
        border: 1px solid #1a202c;
        padding: 16px;
      }
      .section-title {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 12px;
        text-transform: uppercase;
        border-bottom: 1px solid #ccc;
        padding-bottom: 4px;
      }
      .field-row {
        display: flex;
        margin-bottom: 8px;
      }
      .field-label {
        font-weight: bold;
        min-width: 120px;
        margin-right: 8px;
      }
      .field-value {
        flex: 1;
        border-bottom: 1px solid #ccc;
        min-height: 20px;
        padding-bottom: 2px;
      }
      .address-section {
        margin-bottom: 24px;
      }
      .address-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
      }
      .address-box {
        border: 2px solid #1a202c;
        padding: 16px;
        min-height: 120px;
      }
      .address-title {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 8px;
        text-transform: uppercase;
        background-color: #f0f0f0;
        padding: 4px 8px;
        margin: -16px -16px 12px -16px;
      }
      .package-info {
        border: 1px solid #1a202c;
        margin-bottom: 24px;
      }
      .package-header {
        background-color: #f0f0f0;
        padding: 12px;
        font-weight: bold;
        text-transform: uppercase;
        border-bottom: 1px solid #1a202c;
      }
      .package-content {
        padding: 16px;
      }
      .package-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 16px;
      }
      .signature-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
        margin-top: 32px;
      }
      .signature-box {
        border: 1px solid #1a202c;
        padding: 16px;
        min-height: 100px;
      }
      .signature-title {
        font-weight: bold;
        margin-bottom: 16px;
        text-transform: uppercase;
      }
      .signature-line {
        border-bottom: 1px solid #1a202c;
        height: 40px;
        margin-bottom: 8px;
      }
      .footer {
        margin-top: 32px;
        text-align: center;
        font-size: 12px;
        color: #666;
        border-top: 1px solid #ccc;
        padding-top: 16px;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="company-info">
        <div class="company-name">LUMIGO TRANSPORT</div>
        <div class="company-details">
          Professional Delivery Services<br>
          Phone: ************<br>
          www.lumigotransport.ca
        </div>
      </div>
      <div class="document-info">
        <div class="document-title">WAY BILL</div>
        <div class="document-number">WB-${data.wayBillNumber || '2025-001234'}</div>
        <div style="margin-top: 8px; font-size: 12px;">
          Date: ${data.date || new Date().toLocaleDateString()}
        </div>
      </div>
    </div>

    <div class="info-grid">
      <div class="info-section">
        <div class="section-title">Driver & Vehicle Information</div>
        <div class="field-row">
          <div class="field-label">Driver Name:</div>
          <div class="field-value">${data.driverName || ''}</div>
        </div>
        <div class="field-row">
          <div class="field-label">Vehicle No:</div>
          <div class="field-value">${data.vehicleNumber || ''}</div>
        </div>
        <div class="field-row">
          <div class="field-label">Route No:</div>
          <div class="field-value">${data.routeNumber || ''}</div>
        </div>
      </div>
      
      <div class="info-section">
        <div class="section-title">Service Information</div>
        <div class="field-row">
          <div class="field-label">Service Type:</div>
          <div class="field-value">${data.serviceType || 'Standard'}</div>
        </div>
        <div class="field-row">
          <div class="field-label">Pickup Time:</div>
          <div class="field-value">${data.pickupTime || ''}</div>
        </div>
        <div class="field-row">
          <div class="field-label">Delivery Time:</div>
          <div class="field-value">${data.deliveryTime || ''}</div>
        </div>
      </div>
    </div>

    <div class="address-section">
      <div class="address-grid">
        <div class="address-box">
          <div class="address-title">Sender Information</div>
          <div>${data.senderName || 'Sender Name'}</div>
          <div style="margin-top: 8px; line-height: 1.4;">
            ${data.senderAddress || 'Sender Address<br>City, Province, Postal Code'}
          </div>
        </div>
        
        <div class="address-box">
          <div class="address-title">Receiver Information</div>
          <div>${data.receiverName || 'Receiver Name'}</div>
          <div style="margin-top: 8px; line-height: 1.4;">
            ${data.receiverAddress || 'Receiver Address<br>City, Province, Postal Code'}
          </div>
        </div>
      </div>
    </div>

    <div class="package-info">
      <div class="package-header">Package Information</div>
      <div class="package-content">
        <div class="package-grid">
          <div>
            <div class="field-row">
              <div class="field-label">Package Count:</div>
              <div class="field-value">${data.packageCount || '1'}</div>
            </div>
          </div>
          <div>
            <div class="field-row">
              <div class="field-label">Total Weight:</div>
              <div class="field-value">${data.totalWeight || '0 lbs'}</div>
            </div>
          </div>
          <div>
            <div class="field-row">
              <div class="field-label">Declared Value:</div>
              <div class="field-value">${data.declaredValue || '$0.00'}</div>
            </div>
          </div>
        </div>
        
        <div style="margin-top: 16px;">
          <div class="field-row">
            <div class="field-label">COD Amount:</div>
            <div class="field-value">${data.cod || 'N/A'}</div>
          </div>
        </div>
        
        <div style="margin-top: 16px;">
          <div style="font-weight: bold; margin-bottom: 8px;">Special Instructions:</div>
          <div style="border: 1px solid #ccc; padding: 8px; min-height: 60px;">
            ${data.specialInstructions || 'No special instructions'}
          </div>
        </div>
      </div>
    </div>

    <div class="signature-section">
      <div class="signature-box">
        <div class="signature-title">Sender Signature</div>
        <div class="signature-line"></div>
        <div style="font-size: 12px;">
          Date: _________________ Time: _________________
        </div>
      </div>
      
      <div class="signature-box">
        <div class="signature-title">Receiver Signature</div>
        <div class="signature-line"></div>
        <div style="font-size: 12px;">
          Date: _________________ Time: _________________
        </div>
      </div>
    </div>

    <div class="footer">
      This document serves as proof of pickup and delivery. Please retain for your records.<br>
      Lumigo Transport Solutions - Professional Delivery Services
    </div>
  </body>
</html>`;
};
