import { <PERSON>, Get, UseGuards, <PERSON><PERSON>, <PERSON><PERSON>, Param } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiCookieAuth,
  ApiProduces,
} from '@nestjs/swagger';
import { BillsService } from './bills.service';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { Response } from 'express';
import { BillOfLadingData } from './templates/billOfLading';
import { ShippingLabelData } from './templates/shippingLabel';
import { WayBillData } from './templates/wayBill';

@ApiTags('Business - Order - Bills')
@ApiBearerAuth()
@ApiCookieAuth()
@Controller({
  path: '/bills',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class BillsController {
  constructor(private readonly billsService: BillsService) {}

  // Simple endpoint for testing
  @Get()
  @ApiOperation({ summary: 'Generate and return default bill PDF' })
  @ApiOkResponse({ description: 'PDF binary stream' })
  @Header('Content-Type', 'application/pdf')
  @Header('Content-Disposition', 'inline; filename="bill-of-lading.pdf"')
  @ApiProduces('application/pdf')
  async getDefaultBill(@Res() res: Response): Promise<any> {
    const sampleData: BillOfLadingData = {
      trackingNumber: 'TRK-2025-001234',
      submittedDate: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      }),
    };

    const pdfBuffer = await this.billsService.getBillOfLading(sampleData);
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': 'inline; filename="bill-of-lading.pdf"',
      'Content-Length': pdfBuffer.length,
    });
    res.send(pdfBuffer);
  }

  @Get(':billName')
  @ApiOperation({ summary: 'Generate and return bill PDF' })
  @ApiOkResponse({ description: 'PDF binary stream' })
  @Header('Content-Type', 'application/pdf')
  @Header('Content-Disposition', 'inline; filename="bill-of-lading.pdf"')
  @ApiProduces('application/pdf')
  async getBill(
    @Param('billName') billName: OrderBills,
    @Res() res: Response,
  ): Promise<any> {
    let sampleData: any;

    // Generate appropriate sample data based on bill type
    switch (billName) {
      case OrderBills.BILL_OF_LADING:
        sampleData = {
          trackingNumber: 'TRK-2025-001234',
          submittedDate: new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          }),
          senderName: 'Lumigo Transport Solutions',
          senderAddress: '123 Main Street<br />Montreal, QC H1A 1A1',
          receiverName: 'ABC Company Ltd.',
          receiverAddress: '456 Business Ave<br />Toronto, ON M5V 3A8',
          serviceLevel: 'Express',
          collectDate: '12/08/2025, 10:00 AM',
          deliveryDate: '12/08/2025, 2:00 PM',
          description: '2 Packages + 1 Box = Total: 3',
          quantity: '3',
          weight: '15 lbs',
          dimensions: '24L x 18W x 12H',
          declaredValue: '$2,500.00',
          poNumber: 'PO-2025-5678',
          department: 'Shipping',
          refNumber: 'REF-ABC-001',
        } as BillOfLadingData;
        break;

      case OrderBills.WAY_BILL:
        sampleData = {
          wayBillNumber: '2025-001234',
          date: new Date().toLocaleDateString(),
          driverName: 'John Smith',
          vehicleNumber: 'VH-001',
          routeNumber: 'RT-MTL-TOR',
          senderName: 'Lumigo Transport Solutions',
          senderAddress: '123 Main Street<br>Montreal, QC H1A 1A1',
          receiverName: 'ABC Company Ltd.',
          receiverAddress: '456 Business Ave<br>Toronto, ON M5V 3A8',
          packageCount: '3',
          totalWeight: '15 lbs',
          serviceType: 'Express Delivery',
          specialInstructions: 'Handle with care. Fragile items inside.',
          pickupTime: '10:00 AM',
          deliveryTime: '2:00 PM',
          cod: 'N/A',
          declaredValue: '$2,500.00',
        } as WayBillData;
        break;

      case OrderBills.SHIPPING_LABEL:
        sampleData = {
          trackingNumber: 'TRK-2025-001234',
          senderName: 'Lumigo Transport Solutions',
          senderAddress: '123 Main Street<br>Montreal, QC H1A 1A1',
          receiverName: 'ABC Company Ltd.',
          receiverAddress: '456 Business Ave<br>Toronto, ON M5V 3A8',
          serviceLevel: 'EXPRESS',
          weight: '15 lbs',
          deliveryDate: 'Same Day',
          specialInstructions: 'Handle with care. Fragile items.',
        } as ShippingLabelData;
        break;

      default:
        sampleData = {};
    }

    const pdfBuffer = await this.billsService.getBill(billName, sampleData);
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `inline; filename="${billName}.pdf"`,
      'Content-Length': pdfBuffer.length,
    });
    res.send(pdfBuffer);
  }
}

export enum OrderBills {
  WAY_BILL = 'wayBill',
  BILL_OF_LADING = 'billOfLading',
  SHIPPING_LABEL = 'shippingLabel',
}
